"""
DataSmith AI - GenAI Intern Assignment
Post Discharge Medical AI Assistant - FastAPI Backend

This module implements the FastAPI backend server for the multi-agent medical AI system.
It provides REST API endpoints for patient interactions, session management, and
communication with the LangGraph-based agent system.

Author: DataSmith AI GenAI Intern
Purpose: Interview Assignment - Multi-Agent Medical AI POC
Architecture: FastAPI + LangGraph + ChromaDB + Gemini LLM
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import logging
from agents import MedicalAgentSystem

# Configure comprehensive logging for the medical AI system
# This logs all API requests, agent interactions, and system events
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('medical_ai_system.log'),  # Log to file for audit trail
        logging.StreamHandler()  # Also log to console
    ]
)
logger = logging.getLogger(__name__)

# Initialize FastAPI application with metadata for the medical AI system
app = FastAPI(
    title="Post Discharge Medical AI Assistant",
    description="Multi-Agent AI System for Post-Discharge Patient Care",
    version="1.0.0",
    docs_url="/docs",  # Swagger UI documentation
    redoc_url="/redoc"  # ReDoc documentation
)

class ChatMessage(BaseModel):
    """
    Request model for chat messages from patients

    Attributes:
        message (str): The patient's message/question
        patient_name (Optional[str]): Patient name for identification and data retrieval
    """
    message: str = Field(..., description="Patient's message or medical question", min_length=1)
    patient_name: Optional[str] = Field(None, description="Patient name for identification")

class ChatResponse(BaseModel):
    """
    Response model for AI agent responses

    Attributes:
        response (str): The AI agent's response to the patient
        agent (str): Which agent handled the request (Receptionist/Clinical)
        sources (Optional[List[str]]): Citations for medical information provided
    """
    response: str = Field(..., description="AI agent response to patient")
    agent: str = Field(..., description="Agent that handled the request")
    sources: Optional[List[str]] = Field(None, description="Sources cited for medical information")

# Initialize the multi-agent medical system
# This creates the LangGraph workflow with Receptionist and Clinical agents
logger.info("Initializing Medical Agent System...")
agent_system = MedicalAgentSystem()
logger.info("Medical Agent System initialized successfully")

# Session storage for maintaining patient conversation state
# In production, this would use Redis or a proper session store
sessions: Dict[str, Dict[str, Any]] = {}

@app.post("/chat", response_model=ChatResponse)
async def chat(message: ChatMessage):
    """
    Main chat endpoint for patient interactions with the medical AI system

    This endpoint:
    1. Manages patient sessions and conversation state
    2. Routes messages through the LangGraph multi-agent workflow
    3. Handles patient identification and data retrieval
    4. Returns responses from either Receptionist or Clinical agents

    Args:
        message (ChatMessage): Patient message with optional name

    Returns:
        ChatResponse: AI response with agent info and sources

    Raises:
        HTTPException: If there's an error processing the message
    """
    session_id = "default"  # In production, use proper session management

    try:
        logger.info(f"Processing chat message: {message.message[:50]}...")

        # Initialize new session if doesn't exist
        if session_id not in sessions:
            logger.info("Creating new patient session")
            sessions[session_id] = agent_system.create_new_session(message.patient_name)

        state = sessions[session_id]

        # Handle patient name changes (patient switching)
        if message.patient_name and message.patient_name != state.get("patient_name"):
            logger.info(f"Patient switched to: {message.patient_name}")
            state = agent_system.create_new_session(message.patient_name)
            sessions[session_id] = state

        # Process message through the multi-agent system
        # This invokes the LangGraph workflow with Receptionist and Clinical agents
        result = agent_system.process_message(state, message.message)

        # Update session state with conversation history
        sessions[session_id] = result["state"]

        logger.info(f"Response generated by {result['agent']}")

        return ChatResponse(
            response=result["response"],
            agent=result["agent"],
            sources=result["sources"]
        )

    except Exception as e:
        logger.error(f"Error processing chat message: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error processing your message"
        )

@app.post("/clear-session")
async def clear_session():
    """
    Clear patient session and conversation history

    This endpoint allows patients to start fresh conversations
    and is called when the "Clear Chat" button is pressed in the UI.

    Returns:
        dict: Status message confirming session clearance
    """
    session_id = "default"

    try:
        if session_id in sessions:
            logger.info("Clearing patient session")
            del sessions[session_id]
            return {"status": "Session cleared successfully"}
        else:
            return {"status": "No active session to clear"}

    except Exception as e:
        logger.error(f"Error clearing session: {str(e)}")
        raise HTTPException(status_code=500, detail="Error clearing session")

@app.get("/")
async def root():
    """
    Root endpoint providing API information

    Returns basic information about the medical AI assistant API
    for health checks and API discovery.

    Returns:
        dict: API welcome message and status
    """
    return {
        "message": "Post Discharge Medical AI Assistant API",
        "version": "1.0.0",
        "status": "active",
        "agents": ["Receptionist Agent", "Clinical AI Agent"],
        "features": ["RAG", "Web Search", "Patient Data Retrieval", "Multi-Agent Workflow"]
    }

@app.get("/health")
async def health_check():
    """
    Health check endpoint for monitoring system status

    Returns:
        dict: System health status and component availability
    """
    try:
        # Basic health check - could be expanded to check database, vector DB, etc.
        return {
            "status": "healthy",
            "agents_initialized": agent_system is not None,
            "active_sessions": len(sessions)
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {"status": "unhealthy", "error": str(e)}

# Application startup and server configuration
if __name__ == "__main__":
    """
    Run the FastAPI server directly

    This starts the medical AI assistant API server on localhost:8000
    For production deployment, use a proper ASGI server like Gunicorn
    """
    import uvicorn

    logger.info("Starting Post Discharge Medical AI Assistant API server...")
    uvicorn.run(
        app,
        host="0.0.0.0",  # Accept connections from any IP
        port=8000,       # Standard port for the API
        log_level="info",
        reload=False     # Disable reload for direct execution
    )
