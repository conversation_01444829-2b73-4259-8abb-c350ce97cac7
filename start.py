#!/usr/bin/env python3
"""
<PERSON><PERSON>mith AI - Medical Assistant Startup Script
Simple one-command startup for the complete system
"""

import subprocess
import sys
import time
import os

def start_backend():
    """Start FastAPI backend server"""
    print("🚀 Starting FastAPI Backend...")
    backend_cmd = [sys.executable, "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    return subprocess.Popen(backend_cmd)

def start_frontend():
    """Start Streamlit frontend server"""
    print("🌐 Starting Streamlit Frontend...")
    frontend_cmd = [sys.executable, "-m", "streamlit", "run", "frontend.py", "--server.port", "8503"]
    return subprocess.Popen(frontend_cmd)

def main():
    print("=" * 60)
    print("🏥 DataSmith AI - Medical Assistant")
    print("🎯 GenAI Intern Assignment")
    print("=" * 60)
    
    try:
        # Start backend
        backend_process = start_backend()
        time.sleep(3)  # Wait for backend to initialize
        
        # Start frontend
        frontend_process = start_frontend()
        time.sleep(2)
        
        print("\n" + "=" * 60)
        print("✅ SYSTEM READY!")
        print("📍 Web UI: http://localhost:8503")
        print("📖 API Docs: http://localhost:8000/docs")
        print("=" * 60)
        print("Press Ctrl+C to stop both servers")
        print("=" * 60)
        
        # Wait for processes
        backend_process.wait()
        frontend_process.wait()
        
    except KeyboardInterrupt:
        print("\n🛑 Stopping servers...")
        backend_process.terminate()
        frontend_process.terminate()
        print("✅ Servers stopped successfully!")

if __name__ == "__main__":
    main()
