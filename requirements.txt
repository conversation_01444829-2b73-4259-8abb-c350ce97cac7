# DataSmith AI - GenAI Intern Assignment
# Post Discharge Medical AI Assistant - Dependencies
#
# This file specifies all Python dependencies required for the multi-agent
# medical AI system including FastAPI backend, Streamlit frontend, LangGraph
# workflow, ChromaDB vector database, and Google Gemini LLM integration.

# ==================== WEB FRAMEWORK AND API ====================

# FastAPI - Modern, fast web framework for building APIs
# Used for: REST API endpoints, request/response handling, session management
fastapi==0.104.1

# Uvicorn - ASGI server for FastAPI
# Used for: Running the FastAPI application server
uvicorn==0.24.0

# ==================== FRONTEND INTERFACE ====================

# Streamlit - Framework for building web applications
# Used for: Patient chat interface, UI components, session state management
streamlit==1.28.1

# ==================== AI AND LLM INTEGRATION ====================

# Google Generative AI - Official Google AI SDK
# Used for: Gemini LLM integration for natural language processing
google-generativeai==0.3.2

# LangChain Google GenAI - LangChain integration for Google models
# Used for: LangChain compatibility with Gemini models
langchain-google-genai

# ==================== MULTI-AGENT WORKFLOW ====================

# LangGraph - Framework for building stateful multi-agent workflows
# Used for: Agent orchestration, state management, workflow routing
langgraph

# LangChain - Framework for developing LLM applications
# Used for: Message handling, agent abstractions, tool integration
langchain

# ==================== DATA VALIDATION AND MODELS ====================

# Pydantic - Data validation using Python type annotations
# Used for: API request/response models, data validation, type safety
pydantic==2.5.0

# ==================== CONFIGURATION AND ENVIRONMENT ====================

# Python-dotenv - Load environment variables from .env file
# Used for: API key management, configuration settings
python-dotenv==1.0.0

# ==================== HTTP REQUESTS AND WEB SEARCH ====================

# Requests - HTTP library for Python
# Used for: Web search API calls (SERP API), external service integration
requests==2.31.0

# ==================== PDF PROCESSING ====================

# PyPDF2 - PDF processing library
# Used for: Loading and extracting text from comprehensive nephrology PDF
PyPDF2==3.0.1

# ==================== VECTOR DATABASE (IMPLICIT DEPENDENCIES) ====================

# ChromaDB - Vector database for embeddings and semantic search
# Note: ChromaDB is automatically installed with LangChain
# Used for: RAG implementation, nephrology knowledge storage, semantic search

# ==================== ADDITIONAL IMPLICIT DEPENDENCIES ====================

# SQLite3 - Built into Python standard library
# Used for: Patient data storage, discharge report management

# JSON - Built into Python standard library
# Used for: Data serialization, patient record format

# Logging - Built into Python standard library
# Used for: Comprehensive system logging, audit trail

# ==================== INSTALLATION INSTRUCTIONS ====================

# To install all dependencies:
# pip install -r requirements.txt
#
# For development with auto-reload:
# pip install -r requirements.txt
# python run.py
#
# For production deployment:
# pip install -r requirements.txt
# uvicorn main:app --host 0.0.0.0 --port 8000
# streamlit run frontend.py --server.port 8503
